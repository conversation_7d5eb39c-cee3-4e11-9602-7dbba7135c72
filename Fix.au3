#RequireAdmin
#include <GUIConstantsEx.au3>
#include <StaticConstants.au3>
#include <ProgressConstants.au3>
#include <MsgBoxConstants.au3>
#include <EditConstants.au3>
#include <ButtonConstants.au3>
#include <WindowsConstants.au3>
#include <TabConstants.au3>
#include <File.au3>
#include <Date.au3>

; Program bilgileri
Global Const $PROGRAM_TITLE = "Windows Araçları Pro v3.0"
Global Const $PROGRAM_WIDTH = 900
Global Const $PROGRAM_HEIGHT = 750

; GUI kontrolleri
Global $hGUI, $hTab, $btnRandomize, $btnExit, $btnCustomize
Global $progressBar, $lblStatus, $editLog, $chkComputerName, $chkMachineGuid
Global $chkProductID, $chkInstallationDate, $chkBuildGuid, $chkMacAddress, $btnClearLog
Global $lblComputerNameOld, $lblMachineGuidOld, $lblProductIDOld, $lblInstallDateOld, $lblBuildGuidOld, $lblMacAddressOld
Global $lblComputerNameNew, $lblMachineGuidNew, $lblProductIDNew, $lblInstallDateNew, $lblBuildGuidNew, $lblMacAddressNew

; Windows 11 özelleştirme kontrolleri
Global $chkOldContextMenu, $chkDesktopIcons, $chkDarkTheme, $chkTaskbarSettings
Global $chkFileExtensions, $chkPerformanceOptimization, $chkWallpaper, $progressBarCustom, $lblStatusCustom, $editLogCustom, $btnClearLogCustom

; Değişkenler
Global $isProcessing = False

; Önceki değerleri saklamak için global değişkenler
Global $oldComputerName, $oldMachineGuid, $oldProductID, $oldInstallDate, $oldBuildGuid, $oldMacAddress
Global $newComputerName, $newMachineGuid, $newProductID, $newInstallDate, $newBuildGuid, $newMacAddress

; Ana fonksiyon
Main()

Func Main()
    ; Başlangıçta mevcut değerleri kaydet
    StoreCurrentValues()

    CreateGUI()
    ShowGUI()

    While 1
        $nMsg = GUIGetMsg()
        Switch $nMsg
            Case $GUI_EVENT_CLOSE, $btnExit
                ExitLoop
            Case $btnRandomize
                If Not $isProcessing Then
                    StartRandomization()
                EndIf
            Case $btnClearLog
                GUICtrlSetData($editLog, "")
            Case $btnCustomize
                If Not $isProcessing Then
                    StartWindowsCustomization()
                EndIf
            Case $btnClearLogCustom
                GUICtrlSetData($editLogCustom, "")
        EndSwitch
    WEnd

    GUIDelete($hGUI)
EndFunc

Func StoreCurrentValues()
    ; Mevcut değerleri kaydet
    $oldComputerName = @ComputerName
    $oldMachineGuid = GetMachineGUID()
    $oldProductID = GetProductID()
    $oldInstallDate = GetInstallationDate()
    $oldBuildGuid = GetBuildGUID()
    $oldMacAddress = GetCurrentMacAddress()
EndFunc

Func CreateGUI()
    ; Ana pencere oluştur
    $hGUI = GUICreate($PROGRAM_TITLE, $PROGRAM_WIDTH, $PROGRAM_HEIGHT, -1, -1, -1, $WS_EX_ACCEPTFILES)
    GUISetBkColor(0xF0F0F0)

    ; Başlık
    GUICtrlCreateLabel("Windows Araçları Pro v3.0", 20, 10, 810, 30)
    GUICtrlSetFont(-1, 16, 800)
    GUICtrlSetColor(-1, 0x000080)

    ; Tab kontrolü oluştur
    $hTab = GUICtrlCreateTab(20, 50, 860, 650)

    ; İlk sekme - Hardware ID Randomizer
    GUICtrlCreateTabItem("🎲 Hardware ID Randomizer")
    CreateHardwareIDTab()

    ; İkinci sekme - Windows 11 Özelleştirme
    GUICtrlCreateTabItem("⚙️ Windows 11 Özelleştirme")
    CreateWindowsCustomizationTab()

    ; Tab'ları kapat
    GUICtrlCreateTabItem("")

EndFunc

Func CreateHardwareIDTab()
    ; Uyarı mesajı
    GUICtrlCreateLabel("⚠️ UYARI: Bu araç sistem ayarlarını değiştirir. Kullanmadan önce sistem yedeği alın!", 40, 85, 710, 20)
    GUICtrlSetColor(-1, 0xFF0000)
    GUICtrlSetFont(-1, 9, 600)

    ; Seçenekler grubu
    GUICtrlCreateGroup("Değiştirilecek Öğeler", 40, 115, 200, 175)
    $chkComputerName = GUICtrlCreateCheckbox("Bilgisayar Adı", 55, 140, 120, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkMachineGuid = GUICtrlCreateCheckbox("Machine GUID", 55, 165, 120, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkProductID = GUICtrlCreateCheckbox("Product ID", 55, 190, 120, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkInstallationDate = GUICtrlCreateCheckbox("Installation Date", 55, 215, 120, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkBuildGuid = GUICtrlCreateCheckbox("Build GUID", 55, 240, 120, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkMacAddress = GUICtrlCreateCheckbox("WiFi MAC Adresi", 55, 265, 120, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    ; Karşılaştırma tablosu
    GUICtrlCreateGroup("Öncesi / Sonrası Karşılaştırma", 260, 115, 590, 175)

    ; Başlıklar
    GUICtrlCreateLabel("Öğe", 270, 140, 80, 15)
    GUICtrlSetFont(-1, 9, 600)
    GUICtrlCreateLabel("Önceki Değer", 370, 140, 170, 15)
    GUICtrlSetFont(-1, 9, 600)
    GUICtrlCreateLabel("Yeni Değer", 550, 140, 170, 15)
    GUICtrlSetFont(-1, 9, 600)

    ; Çizgi
    GUICtrlCreateLabel("─────────────────────────────────────────────────────────────────────────────", 270, 155, 570, 10)

    ; Bilgisayar Adı
    GUICtrlCreateLabel("PC Adı:", 270, 170, 80, 15)
    $lblComputerNameOld = GUICtrlCreateLabel($oldComputerName, 370, 170, 170, 15)
    GUICtrlSetColor(-1, 0x000080)
    $lblComputerNameNew = GUICtrlCreateLabel("─", 550, 170, 170, 15)
    GUICtrlSetColor(-1, 0x008000)

    ; Machine GUID
    GUICtrlCreateLabel("M.GUID:", 270, 185, 80, 15)
    $lblMachineGuidOld = GUICtrlCreateLabel(StringLeft($oldMachineGuid, 25) & "...", 370, 185, 170, 15)
    GUICtrlSetColor(-1, 0x000080)
    $lblMachineGuidNew = GUICtrlCreateLabel("─", 550, 185, 170, 15)
    GUICtrlSetColor(-1, 0x008000)

    ; Product ID
    GUICtrlCreateLabel("Prod.ID:", 270, 200, 80, 15)
    $lblProductIDOld = GUICtrlCreateLabel($oldProductID, 370, 200, 170, 15)
    GUICtrlSetColor(-1, 0x000080)
    $lblProductIDNew = GUICtrlCreateLabel("─", 550, 200, 170, 15)
    GUICtrlSetColor(-1, 0x008000)

    ; Installation Date
    GUICtrlCreateLabel("Inst.Date:", 270, 215, 80, 15)
    $lblInstallDateOld = GUICtrlCreateLabel($oldInstallDate, 370, 215, 170, 15)
    GUICtrlSetColor(-1, 0x000080)
    $lblInstallDateNew = GUICtrlCreateLabel("─", 550, 215, 170, 15)
    GUICtrlSetColor(-1, 0x008000)

    ; Build GUID
    GUICtrlCreateLabel("B.GUID:", 270, 230, 80, 15)
    $lblBuildGuidOld = GUICtrlCreateLabel(StringLeft($oldBuildGuid, 25) & "...", 370, 230, 170, 15)
    GUICtrlSetColor(-1, 0x000080)
    $lblBuildGuidNew = GUICtrlCreateLabel("─", 550, 230, 170, 15)
    GUICtrlSetColor(-1, 0x008000)

    ; MAC Address
    GUICtrlCreateLabel("MAC Addr:", 270, 245, 80, 15)
    $lblMacAddressOld = GUICtrlCreateLabel($oldMacAddress, 370, 245, 170, 15)
    GUICtrlSetColor(-1, 0x000080)
    $lblMacAddressNew = GUICtrlCreateLabel("─", 550, 245, 170, 15)
    GUICtrlSetColor(-1, 0x008000)

    ; Durum göstergesi
    GUICtrlCreateLabel("Durum:", 270, 265, 80, 15)
    GUICtrlCreateLabel("🔴 Değiştirilmedi", 370, 265, 170, 15)
    GUICtrlSetColor(-1, 0x666666)
    GUICtrlCreateLabel("🟢 Yeni değer", 550, 265, 170, 15)
    GUICtrlSetColor(-1, 0x666666)

    ; Butonlar
    $btnRandomize = GUICtrlCreateButton("🎲 Randomize Hardware ID", 40, 305, 160, 35)
    GUICtrlSetFont(-1, 10, 600)
    GUICtrlSetBkColor(-1, 0x4CAF50)
    GUICtrlSetColor(-1, 0xFFFFFF)

    ; İlerleme çubuğu
    GUICtrlCreateLabel("İşlem Durumu:", 40, 355, 100, 15)
    $progressBar = GUICtrlCreateProgress(40, 375, 810, 20, $PBS_SMOOTH)

    ; Durum etiketi
    $lblStatus = GUICtrlCreateLabel("Hazır...", 40, 405, 810, 20)
    GUICtrlSetColor(-1, 0x006600)
    GUICtrlSetFont(-1, 9, 600)

    ; Log alanı
    GUICtrlCreateLabel("İşlem Logları:", 40, 435, 100, 15)
    $btnClearLog = GUICtrlCreateButton("Temizle", 770, 430, 60, 20)
    $editLog = GUICtrlCreateEdit("Hardware ID Randomizer hazır..." & @CRLF, 40, 455, 810, 210, BitOR($ES_READONLY, $ES_MULTILINE, $ES_AUTOVSCROLL))
    GUICtrlSetFont(-1, 8)
EndFunc

Func CreateWindowsCustomizationTab()
    ; Uyarı mesajı
    GUICtrlCreateLabel("⚠️ UYARI: Bu araç Windows 11 sistem ayarlarını değiştirir. Yönetici yetkisi gereklidir!", 40, 85, 810, 20)
    GUICtrlSetColor(-1, 0xFF0000)
    GUICtrlSetFont(-1, 9, 600)

    ; Özelleştirme seçenekleri grubu
    GUICtrlCreateGroup("Windows 11 Özelleştirme Seçenekleri", 40, 115, 400, 250)

    $chkOldContextMenu = GUICtrlCreateCheckbox("🖱️ Eski Sağ Tık Menüsünü Etkinleştir", 55, 140, 250, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkDesktopIcons = GUICtrlCreateCheckbox("🖥️ Masaüstü Simgelerini Göster (Bu PC, Kullanıcı)", 55, 165, 300, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkDarkTheme = GUICtrlCreateCheckbox("🌙 Karanlık Temayı Etkinleştir", 55, 190, 200, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkTaskbarSettings = GUICtrlCreateCheckbox("📊 Görev Çubuğunu Optimize Et (Arama/Görev Görünümü)", 55, 215, 350, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkFileExtensions = GUICtrlCreateCheckbox("📁 Dosya Uzantılarını Göster", 55, 240, 200, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkPerformanceOptimization = GUICtrlCreateCheckbox("⚡ Performans Optimizasyonu (Görsel Efektler)", 55, 265, 300, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    $chkWallpaper = GUICtrlCreateCheckbox("🖼️ Wallpaper.jpg'yi Arka Plan Yap", 55, 290, 250, 20)
    GUICtrlSetState(-1, $GUI_CHECKED)

    ; Özellik açıklamaları
    GUICtrlCreateGroup("Özellik Açıklamaları", 460, 115, 390, 250)

    GUICtrlCreateLabel("• Eski Sağ Tık: Windows 10 tarzı klasik menü", 475, 140, 360, 15)
    GUICtrlCreateLabel("• Masaüstü Simgeleri: 'Bu PC' ve 'Kullanıcı Dosyaları'", 475, 160, 360, 15)
    GUICtrlCreateLabel("• Karanlık Tema: Sistem genelinde koyu tema", 475, 180, 360, 15)
    GUICtrlCreateLabel("• Görev Çubuğu: Arama kutusu ve görev görünümü gizleme", 475, 200, 360, 15)
    GUICtrlCreateLabel("• Dosya Uzantıları: .txt, .exe gibi uzantıları göster", 475, 220, 360, 15)
    GUICtrlCreateLabel("• Performans: Animasyonları kapat, hızı artır", 475, 240, 360, 15)
    GUICtrlCreateLabel("• Wallpaper: Script dizinindeki Wallpaper.jpg'yi uygular", 475, 260, 360, 15)
    GUICtrlCreateLabel("• ClearType ve küçük resimler korunur", 475, 280, 360, 15)
    GUICtrlCreateLabel("• Sistem öncelik ayarları optimize edilir", 475, 300, 360, 15)
    GUICtrlCreateLabel("• Explorer otomatik yeniden başlatılır", 475, 320, 360, 15)

    ; Buton
    $btnCustomize = GUICtrlCreateButton("⚙️ Windows 11'i Özelleştir", 40, 380, 180, 35)
    GUICtrlSetFont(-1, 10, 600)
    GUICtrlSetBkColor(-1, 0x2196F3)
    GUICtrlSetColor(-1, 0xFFFFFF)

    ; İlerleme çubuğu
    GUICtrlCreateLabel("İşlem Durumu:", 40, 430, 100, 15)
    $progressBarCustom = GUICtrlCreateProgress(40, 450, 810, 20, $PBS_SMOOTH)

    ; Durum etiketi
    $lblStatusCustom = GUICtrlCreateLabel("Hazır...", 40, 480, 810, 20)
    GUICtrlSetColor(-1, 0x006600)
    GUICtrlSetFont(-1, 9, 600)

    ; Log alanı
    GUICtrlCreateLabel("İşlem Logları:", 40, 510, 100, 15)
    $btnClearLogCustom = GUICtrlCreateButton("Temizle", 770, 505, 60, 20)
    $editLogCustom = GUICtrlCreateEdit("Windows 11 Özelleştirme hazır..." & @CRLF, 40, 530, 810, 135, BitOR($ES_READONLY, $ES_MULTILINE, $ES_AUTOVSCROLL))
    GUICtrlSetFont(-1, 8)
EndFunc

Func ShowGUI()
    GUISetState(@SW_SHOW, $hGUI)

    ; Ana sekme için çıkış butonu
    $btnExit = GUICtrlCreateButton("❌ Çıkış", 750, 10, 100, 30)
    GUICtrlSetFont(-1, 9, 400)
    GUICtrlSetBkColor(-1, 0xF44336)
    GUICtrlSetColor(-1, 0xFFFFFF)

    WriteLog("GUI başarıyla yüklendi.")
    WriteLog("⚠️ Lütfen işlem öncesi sistem yedeği aldığınızdan emin olun!")
    WriteLog("📊 Mevcut değerler kayıtlara alındı - değişiklik karşılaştırması hazır.")
    If $oldMacAddress = "Bulunamadı" Then
        WriteLog("⚠️ WiFi adaptörü bulunamadı - MAC adresi değiştirilemeyecek.")
    EndIf

    WriteLogCustom("Windows 11 Özelleştirme aracı hazır.")
    WriteLogCustom("⚠️ Bu araç sistem registry'sini değiştirir - yedek alın!")
    WriteLogCustom("📋 Özelleştirmek istediğiniz seçenekleri işaretleyin.")
    WriteLogCustom("🖼️ Wallpaper.jpg dosyası script dizininde bulunmalıdır.")
EndFunc

Func StartRandomization()
    If MsgBox($MB_YESNO + $MB_ICONWARNING, "Onay", "Hardware ID randomization işlemini başlatmak istediğinizden emin misiniz?" & @CRLF & @CRLF & "Bu işlem geri alınamaz değişiklikler yapacaktır!") = $IDNO Then
        Return
    EndIf

    $isProcessing = True
    GUICtrlSetData($btnRandomize, "⏳ İşlem Devam Ediyor...")
    GUICtrlSetState($btnRandomize, $GUI_DISABLE)

    WriteLog("=== Hardware ID Randomization Başlatıldı ===")
    WriteLog("📋 ÖNCEKI DEĞERLER:")
    WriteLog("   • Bilgisayar Adı: " & $oldComputerName)
    WriteLog("   • Machine GUID: " & $oldMachineGuid)
    WriteLog("   • Product ID: " & $oldProductID)
    WriteLog("   • Installation Date: " & $oldInstallDate)
    WriteLog("   • Build GUID: " & $oldBuildGuid)
    WriteLog("   • MAC Adresi: " & $oldMacAddress)
    WriteLog("─────────────────────────────────────────")

    UpdateProgress(0, "İşlem başlatılıyor...")

    Local $step = 0
    Local $totalSteps = 6
    Local $hasError = False

    ; 1. Bilgisayar adını değiştir
    If GUICtrlRead($chkComputerName) = $GUI_CHECKED Then
        $step += 1
        UpdateProgress(($step / $totalSteps) * 100, "Bilgisayar adı değiştiriliyor...")
        If Not ChangeComputerName() Then $hasError = True
        Sleep(500)
    EndIf

    ; 2. Machine GUID değiştir
    If GUICtrlRead($chkMachineGuid) = $GUI_CHECKED Then
        $step += 1
        UpdateProgress(($step / $totalSteps) * 100, "Machine GUID değiştiriliyor...")
        If Not ChangeMachineGUID() Then $hasError = True
        Sleep(500)
    EndIf

    ; 3. Product ID değiştir
    If GUICtrlRead($chkProductID) = $GUI_CHECKED Then
        $step += 1
        UpdateProgress(($step / $totalSteps) * 100, "Product ID değiştiriliyor...")
        If Not ChangeProductID() Then $hasError = True
        Sleep(500)
    EndIf

    ; 4. Installation Date değiştir
    If GUICtrlRead($chkInstallationDate) = $GUI_CHECKED Then
        $step += 1
        UpdateProgress(($step / $totalSteps) * 100, "Installation Date güncelleniyor...")
        If Not ChangeInstallationDate() Then $hasError = True
        Sleep(500)
    EndIf

    ; 5. Build GUID değiştir
    If GUICtrlRead($chkBuildGuid) = $GUI_CHECKED Then
        $step += 1
        UpdateProgress(($step / $totalSteps) * 100, "Build GUID güncelleniyor...")
        If Not ChangeBuildGUID() Then $hasError = True
        Sleep(500)
    EndIf

    ; 6. MAC Adresi değiştir
    If GUICtrlRead($chkMacAddress) = $GUI_CHECKED Then
        $step += 1
        UpdateProgress(($step / $totalSteps) * 100, "WiFi MAC adresi değiştiriliyor...")
        If Not ChangeMacAddress() Then $hasError = True
        Sleep(500)
    EndIf

    ; Sonuç özeti
    WriteLog("─────────────────────────────────────────")
    WriteLog("📋 YENİ DEĞERLER:")
    If $newComputerName <> "" Then WriteLog("   • Bilgisayar Adı: " & $oldComputerName & " → " & $newComputerName)
    If $newMachineGuid <> "" Then WriteLog("   • Machine GUID: " & $oldMachineGuid & " → " & $newMachineGuid)
    If $newProductID <> "" Then WriteLog("   • Product ID: " & $oldProductID & " → " & $newProductID)
    If $newInstallDate <> "" Then WriteLog("   • Installation Date: " & $oldInstallDate & " → " & $newInstallDate)
    If $newBuildGuid <> "" Then WriteLog("   • Build GUID: " & $oldBuildGuid & " → " & $newBuildGuid)
    If $newMacAddress <> "" Then WriteLog("   • MAC Adresi: " & $oldMacAddress & " → " & $newMacAddress)

    If $hasError Then
        UpdateProgress(0, "❌ İşlem sırasında hatalar oluştu!")
        WriteLog("⚠️ Bazı işlemler başarısız oldu. Log'ları kontrol edin.")
        MsgBox($MB_ICONWARNING, "Uyarı", "İşlem sırasında bazı hatalar oluştu. Log dosyasını kontrol edin.")
    Else
        UpdateProgress(100, "✅ İşlem başarıyla tamamlandı!")
        WriteLog("=== Tüm değişiklikler başarıyla uygulandı ===")
        WriteLog("⚠️ Değişikliklerin etkili olması için sistemi yeniden başlatın!")

        ; Yeniden başlatma onayı
        If MsgBox($MB_YESNO + $MB_ICONQUESTION, "Yeniden Başlatma", "Değişikliklerin etkili olması için sistemi yeniden başlatmanız gerekiyor." & @CRLF & @CRLF & "Şimdi yeniden başlatmak istiyor musunuz?") = $IDYES Then
            WriteLog("Sistem yeniden başlatılıyor...")
            Shutdown(2) ; Restart
        EndIf
    EndIf

    $isProcessing = False
    GUICtrlSetData($btnRandomize, "🎲 Randomize Hardware ID")
    GUICtrlSetState($btnRandomize, $GUI_ENABLE)
EndFunc

Func StartWindowsCustomization()
    If MsgBox($MB_YESNO + $MB_ICONWARNING, "Onay", "Windows 11 özelleştirme işlemini başlatmak istediğinizden emin misiniz?" & @CRLF & @CRLF & "Bu işlem sistem ayarlarını değiştirecektir!") = $IDNO Then
        Return
    EndIf

    $isProcessing = True
    GUICtrlSetData($btnCustomize, "⏳ İşlem Devam Ediyor...")
    GUICtrlSetState($btnCustomize, $GUI_DISABLE)

    WriteLogCustom("=== Windows 11 Özelleştirme Başlatıldı ===")
    UpdateProgressCustom(0, "İşlem başlatılıyor...")

    Local $step = 0
    Local $totalSteps = 7
    Local $hasError = False

    ; 1. Eski sağ tık menüsü
    If GUICtrlRead($chkOldContextMenu) = $GUI_CHECKED Then
        $step += 1
        UpdateProgressCustom(($step / $totalSteps) * 100, "Eski sağ tık menüsü etkinleştiriliyor...")
        If Not EnableOldContextMenu() Then $hasError = True
        Sleep(500)
    EndIf

    ; 2. Masaüstü simgeleri
    If GUICtrlRead($chkDesktopIcons) = $GUI_CHECKED Then
        $step += 1
        UpdateProgressCustom(($step / $totalSteps) * 100, "Masaüstü simgeleri ayarlanıyor...")
        If Not EnableDesktopIcons() Then $hasError = True
        Sleep(500)
    EndIf

    ; 3. Karanlık tema
    If GUICtrlRead($chkDarkTheme) = $GUI_CHECKED Then
        $step += 1
        UpdateProgressCustom(($step / $totalSteps) * 100, "Karanlık tema etkinleştiriliyor...")
        If Not EnableDarkTheme() Then $hasError = True
        Sleep(500)
    EndIf

    ; 4. Görev çubuğu ayarları
    If GUICtrlRead($chkTaskbarSettings) = $GUI_CHECKED Then
        $step += 1
        UpdateProgressCustom(($step / $totalSteps) * 100, "Görev çubuğu optimize ediliyor...")
        If Not OptimizeTaskbar() Then $hasError = True
        Sleep(500)
    EndIf

    ; 5. Dosya uzantıları
    If GUICtrlRead($chkFileExtensions) = $GUI_CHECKED Then
        $step += 1
        UpdateProgressCustom(($step / $totalSteps) * 100, "Dosya uzantıları gösteriliyor...")
        If Not ShowFileExtensions() Then $hasError = True
        Sleep(500)
    EndIf

    ; 6. Performans optimizasyonu
    If GUICtrlRead($chkPerformanceOptimization) = $GUI_CHECKED Then
        $step += 1
        UpdateProgressCustom(($step / $totalSteps) * 100, "Performans optimize ediliyor...")
        If Not OptimizePerformance() Then $hasError = True
        Sleep(500)
    EndIf

    ; 7. Wallpaper değiştir
    If GUICtrlRead($chkWallpaper) = $GUI_CHECKED Then
        $step += 1
        UpdateProgressCustom(($step / $totalSteps) * 100, "Wallpaper ayarlanıyor...")
        If Not SetWallpaper() Then $hasError = True
        Sleep(500)
    EndIf

    ; Explorer'ı yeniden başlat
    UpdateProgressCustom(100, "Explorer yeniden başlatılıyor...")
    RestartExplorer()

    If $hasError Then
        UpdateProgressCustom(0, "❌ İşlem sırasında hatalar oluştu!")
        WriteLogCustom("⚠️ Bazı işlemler başarısız oldu. Log'ları kontrol edin.")
        MsgBox($MB_ICONWARNING, "Uyarı", "İşlem sırasında bazı hatalar oluştu. Log dosyasını kontrol edin.")
    Else
        UpdateProgressCustom(100, "✅ İşlem başarıyla tamamlandı!")
        WriteLogCustom("=== Tüm özelleştirmeler başarıyla uygulandı ===")
        WriteLogCustom("⚠️ Değişikliklerin tam etkisi için sistemi yeniden başlatın!")

        If MsgBox($MB_YESNO + $MB_ICONQUESTION, "Yeniden Başlatma", "Değişikliklerin tam etkisi için sistemi yeniden başlatmanız önerilir." & @CRLF & @CRLF & "Şimdi yeniden başlatmak istiyor musunuz?") = $IDYES Then
            WriteLogCustom("Sistem yeniden başlatılıyor...")
            Shutdown(2) ; Restart
        EndIf
    EndIf

    $isProcessing = False
    GUICtrlSetData($btnCustomize, "⚙️ Windows 11'i Özelleştir")
    GUICtrlSetState($btnCustomize, $GUI_ENABLE)
EndFunc

Func ChangeComputerName()
    $newComputerName = "PC-" & GenerateRandomString(8)
    Local $result = RunWait('powershell.exe -Command "Rename-Computer -NewName ' & $newComputerName & ' -Force"', "", @SW_HIDE)

    If $result = 0 Then
        WriteLog("✅ Bilgisayar adı değiştirildi: " & $oldComputerName & " → " & $newComputerName)
        GUICtrlSetData($lblComputerNameNew, $newComputerName)
        Return True
    Else
        WriteLog("❌ Bilgisayar adı değiştirme hatası!")
        Return False
    EndIf
EndFunc

Func ChangeMachineGUID()
    $newMachineGuid = GenerateGUID()
    Local $result = RegWrite("HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography", "MachineGuid", "REG_SZ", $newMachineGuid)

    If $result = 1 Then
        WriteLog("✅ Machine GUID değiştirildi: " & $oldMachineGuid & " → " & $newMachineGuid)
        GUICtrlSetData($lblMachineGuidNew, StringLeft($newMachineGuid, 25) & "...")
        Return True
    Else
        WriteLog("❌ Machine GUID değiştirme hatası!")
        Return False
    EndIf
EndFunc

Func ChangeProductID()
    $newProductID = GenerateRandomString(5) & "-" & GenerateRandomString(3) & "-" & GenerateRandomString(7) & "-" & GenerateRandomString(5)
    Local $result = RegWrite("HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion", "ProductId", "REG_SZ", $newProductID)

    If $result = 1 Then
        WriteLog("✅ Product ID değiştirildi: " & $oldProductID & " → " & $newProductID)
        GUICtrlSetData($lblProductIDNew, $newProductID)
        Return True
    Else
        WriteLog("❌ Product ID değiştirme hatası!")
        Return False
    EndIf
EndFunc

Func ChangeInstallationDate()
    Local $currentTime = _NowCalc()
    Local $unixTime = _DateDiff('s', "1970/01/01 00:00:00", $currentTime)
    $newInstallDate = _DateTimeFormat($currentTime, 1)
    Local $result = RegWrite("HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion", "InstallDate", "REG_DWORD", $unixTime)

    If $result = 1 Then
        WriteLog("✅ Installation Date güncellendi: " & $oldInstallDate & " → " & $newInstallDate)
        GUICtrlSetData($lblInstallDateNew, $newInstallDate)
        Return True
    Else
        WriteLog("❌ Installation Date güncelleme hatası!")
        Return False
    EndIf
EndFunc

Func ChangeBuildGUID()
    $newBuildGuid = GenerateGUID()
    Local $result = RegWrite("HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion", "BuildGUID", "REG_SZ", $newBuildGuid)

    If $result = 1 Then
        WriteLog("✅ Build GUID güncellendi: " & $oldBuildGuid & " → " & $newBuildGuid)
        GUICtrlSetData($lblBuildGuidNew, StringLeft($newBuildGuid, 25) & "...")
        Return True
    Else
        WriteLog("⚠️ Build GUID bulunamadı (normal)")
        $newBuildGuid = "Bulunamadı"
        GUICtrlSetData($lblBuildGuidNew, "Bulunamadı")
        Return True ; Build GUID yoksa normal kabul et
    EndIf
EndFunc

Func ChangeMacAddress()
    If $oldMacAddress = "Bulunamadı" Then
        WriteLog("❌ WiFi adaptörü bulunamadı - MAC adresi değiştirilemez!")
        Return False
    EndIf

    ; Yeni rastgele MAC adresi oluştur
    $newMacAddress = GenerateRandomMacAddress()

    ; WiFi adaptörünün adını al
    Local $adapterName = GetWifiAdapterName()
    If $adapterName = "" Then
        WriteLog("❌ WiFi adaptör adı bulunamadı!")
        Return False
    EndIf

    WriteLog("📡 WiFi adaptörü bulundu: " & $adapterName)

    ; Basit MAC değiştirme yöntemi - getmac komutu ile mevcut adaptörü bul ve değiştir
    WriteLog("🔄 WiFi MAC adresi değiştiriliyor...")

    ; Registry'de MAC adresini değiştir
    Local $regKey = FindNetworkAdapterRegistry()
    If $regKey <> "" Then
        WriteLog("🔧 Registry anahtarı bulundu")
        Local $cleanMac = StringReplace($newMacAddress, ":", "")
        Local $regResult = RegWrite($regKey, "NetworkAddress", "REG_SZ", $cleanMac)

        If $regResult = 1 Then
            WriteLog("✅ Registry'de MAC adresi güncellendi: " & $cleanMac)
            ; Ağ adaptörünü yeniden başlat
            RunWait('netsh interface set interface "' & $adapterName & '" admin=disable', "", @SW_HIDE)
            Sleep(2000)
            RunWait('netsh interface set interface "' & $adapterName & '" admin=enable', "", @SW_HIDE)
            Sleep(3000)

            WriteLog("✅ WiFi MAC adresi değiştirildi: " & $oldMacAddress & " → " & $newMacAddress)
            GUICtrlSetData($lblMacAddressNew, $newMacAddress)
            Return True
        Else
            WriteLog("❌ Registry MAC adresi güncellenemedi!")
            Return False
        EndIf
    Else
        WriteLog("❌ WiFi adaptör registry anahtarı bulunamadı!")
        Return False
    EndIf
EndFunc

Func GetCurrentMacAddress()
    ; getmac komutunu kullan
    Local $output = ""
    Local $pid = Run('getmac /fo csv /v', "", @SW_HIDE, $STDOUT_CHILD)
    While 1
        $output &= StdoutRead($pid)
        If @error Then ExitLoop
    WEnd

    ; Çıktıyı analiz et ve WiFi/Wireless adaptörünü bul
    Local $lines = StringSplit($output, @CRLF, 1)
    For $i = 2 To $lines[0] ; İlk satır başlık
        If StringInStr($lines[$i], "Wi-Fi") Or StringInStr($lines[$i], "Wireless") Or StringInStr($lines[$i], "WiFi") Then
            Local $fields = StringSplit($lines[$i], '","', 1)
            If $fields[0] >= 3 Then
                Local $mac = StringReplace($fields[3], '"', '')
                If StringLen($mac) = 17 And StringInStr($mac, "-") Then
                    Return StringReplace($mac, "-", ":")
                EndIf
            EndIf
        EndIf
    Next

    Return "Bulunamadı"
EndFunc

Func GetWifiAdapterName()
    ; Basit yöntem - Wi-Fi adaptör adını döndür
    Local $output = ""
    Local $pid = Run('netsh interface show interface', "", @SW_HIDE, $STDOUT_CHILD)
    While 1
        $output &= StdoutRead($pid)
        If @error Then ExitLoop
    WEnd

    Local $lines = StringSplit($output, @CRLF, 1)
    For $i = 1 To $lines[0]
        If StringInStr($lines[$i], "Wi-Fi") Then
            Return "Wi-Fi"
        ElseIf StringInStr($lines[$i], "WiFi") Then
            Return "WiFi"
        ElseIf StringInStr($lines[$i], "Wireless") Then
            Local $parts = StringSplit(StringStripWS($lines[$i], 3), " ", 1)
            If $parts[0] >= 4 Then
                Return $parts[$parts[0]] ; Son kelime genellikle adaptör adı
            EndIf
        EndIf
    Next

    Return "Wi-Fi" ; Varsayılan
EndFunc

Func FindNetworkAdapterRegistry()
    ; Ağ adaptörlerinin registry anahtarını bul
    Local $baseKey = "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}"

    For $i = 0 To 99
        Local $subKey = $baseKey & "\" & StringFormat("%04d", $i)
        Local $desc = RegRead($subKey, "DriverDesc")

        If Not @error And $desc <> "" Then
            ; WiFi ile ilgili adaptör ara
            If StringInStr($desc, "Wi") Or StringInStr($desc, "Wireless") Or StringInStr($desc, "802.11") Then
                Return $subKey
            EndIf
        EndIf
    Next

    Return ""
EndFunc

Func GenerateRandomMacAddress()
    ; Yerel olarak yönetilen MAC adresi oluştur
    Local $mac = ""

    ; İlk oktet - yerel yönetim biti set edilmiş
    Local $firstOctet = Random(2, 254, 1)
    $firstOctet = BitOR($firstOctet, 2) ; Yerel yönetim bitini set et
    $firstOctet = BitAND($firstOctet, 254) ; Multicast bitini temizle

    $mac = StringFormat("%02X", $firstOctet)

    ; Diğer 5 oktet
    For $i = 1 To 5
        $mac &= ":" & StringFormat("%02X", Random(0, 255, 1))
    Next

    Return $mac
EndFunc

Func GenerateRandomString($length)
    Local $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    Local $result = ""

    For $i = 1 To $length
        $result &= StringMid($chars, Random(1, StringLen($chars), 1), 1)
    Next

    Return $result
EndFunc

Func GenerateGUID()
    Local $guid = ""
    $guid &= GenerateRandomString(8) & "-"
    $guid &= GenerateRandomString(4) & "-"
    $guid &= GenerateRandomString(4) & "-"
    $guid &= GenerateRandomString(4) & "-"
    $guid &= GenerateRandomString(12)
    Return $guid
EndFunc

Func GetMachineGUID()
    Local $guid = RegRead("HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Cryptography", "MachineGuid")
    If @error Then Return "Okunamadı"
    Return $guid
EndFunc

Func GetProductID()
    Local $productID = RegRead("HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion", "ProductId")
    If @error Then Return "Okunamadı"
    Return $productID
EndFunc

Func GetInstallationDate()
    Local $installDate = RegRead("HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion", "InstallDate")
    If @error Then Return "Okunamadı"

    ; Unix timestamp'i tarih formatına çevir
    Local $dateTime = _DateAdd('s', $installDate, "1970/01/01 00:00:00")
    Return _DateTimeFormat($dateTime, 1)
EndFunc

Func GetBuildGUID()
    Local $buildGuid = RegRead("HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion", "BuildGUID")
    If @error Then Return "Bulunamadı"
    Return $buildGuid
EndFunc

Func UpdateProgress($percent, $status)
    GUICtrlSetData($progressBar, $percent)
    GUICtrlSetData($lblStatus, $status)
    Sleep(100) ; GUI güncellenmesi için kısa bekleme
EndFunc

Func WriteLog($message)
    Local $timestamp = "[" & @HOUR & ":" & @MIN & ":" & @SEC & "] "
    Local $currentLog = GUICtrlRead($editLog)
    GUICtrlSetData($editLog, $currentLog & $timestamp & $message & @CRLF)

    ; Log alanını en alta kaydır
    GUICtrlSendMsg($editLog, $EM_SETSEL, -1, -1)
    GUICtrlSendMsg($editLog, $EM_SCROLLCARET, 0, 0)
EndFunc

Func WriteLogCustom($message)
    Local $timestamp = "[" & @HOUR & ":" & @MIN & ":" & @SEC & "] "
    Local $currentLog = GUICtrlRead($editLogCustom)
    GUICtrlSetData($editLogCustom, $currentLog & $timestamp & $message & @CRLF)

    ; Log alanını en alta kaydır
    GUICtrlSendMsg($editLogCustom, $EM_SETSEL, -1, -1)
    GUICtrlSendMsg($editLogCustom, $EM_SCROLLCARET, 0, 0)
EndFunc

Func UpdateProgressCustom($percent, $status)
    GUICtrlSetData($progressBarCustom, $percent)
    GUICtrlSetData($lblStatusCustom, $status)
    Sleep(100) ; GUI güncellenmesi için kısa bekleme
EndFunc

; Windows 11 Özelleştirme Fonksiyonları

Func EnableOldContextMenu()
    WriteLogCustom("🖱️ Eski sağ tık menüsü etkinleştiriliyor...")
    Local $result = RegWrite("HKEY_CURRENT_USER\Software\Classes\CLSID\{86ca1aa0-34aa-4e8b-a509-50c905bae2a2}\InprocServer32", "", "REG_SZ", "")

    If $result = 1 Then
        WriteLogCustom("✅ Eski sağ tık menüsü etkinleştirildi")
        Return True
    Else
        WriteLogCustom("❌ Eski sağ tık menüsü etkinleştirme hatası!")
        Return False
    EndIf
EndFunc

Func EnableDesktopIcons()
    WriteLogCustom("🖥️ Masaüstü simgeleri ayarlanıyor...")
    Local $success = True

    ; Registry yollarını oluştur
    Local $paths[3] = ["HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\HideDesktopIcons", _
                      "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\HideDesktopIcons\NewStartPanel", _
                      "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\HideDesktopIcons\ClassicStartMenu"]

    For $i = 0 To 2
        RegWrite($paths[$i], "", "REG_SZ", "")
    Next

    ; Bu PC simgesi
    Local $result1 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\HideDesktopIcons\NewStartPanel", "{20D04FE0-3AEA-1069-A2D8-08002B30309D}", "REG_DWORD", 0)
    Local $result2 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\HideDesktopIcons\ClassicStartMenu", "{20D04FE0-3AEA-1069-A2D8-08002B30309D}", "REG_DWORD", 0)

    ; Kullanıcı Dosyaları simgesi
    Local $result3 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\HideDesktopIcons\NewStartPanel", "{59031a47-3f72-44a7-89c5-5595fe6b30ee}", "REG_DWORD", 0)
    Local $result4 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\HideDesktopIcons\ClassicStartMenu", "{59031a47-3f72-44a7-89c5-5595fe6b30ee}", "REG_DWORD", 0)

    If $result1 = 1 And $result2 = 1 And $result3 = 1 And $result4 = 1 Then
        WriteLogCustom("✅ Masaüstü simgeleri etkinleştirildi (Bu PC, Kullanıcı Dosyaları)")
        Return True
    Else
        WriteLogCustom("❌ Masaüstü simgeleri ayarlama hatası!")
        Return False
    EndIf
EndFunc

Func EnableDarkTheme()
    WriteLogCustom("🌙 Karanlık tema etkinleştiriliyor...")

    ; Registry yolunu oluştur
    RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize", "", "REG_SZ", "")

    Local $result1 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize", "AppsUseLightTheme", "REG_DWORD", 0)
    Local $result2 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Themes\Personalize", "SystemUsesLightTheme", "REG_DWORD", 0)

    If $result1 = 1 And $result2 = 1 Then
        WriteLogCustom("✅ Karanlık tema etkinleştirildi")
        Return True
    Else
        WriteLogCustom("❌ Karanlık tema etkinleştirme hatası!")
        Return False
    EndIf
EndFunc

Func OptimizeTaskbar()
    WriteLogCustom("📊 Görev çubuğu optimize ediliyor...")

    ; Registry yollarını oluştur
    RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Search", "", "REG_SZ", "")
    RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced", "", "REG_SZ", "")

    ; Arama kutusunu kapat
    Local $result1 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Search", "SearchboxTaskbarMode", "REG_DWORD", 0)

    ; Görev Görünümü butonunu kapat
    Local $result2 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced", "ShowTaskViewButton", "REG_DWORD", 0)

    If $result1 = 1 And $result2 = 1 Then
        WriteLogCustom("✅ Görev çubuğu optimize edildi (Arama ve Görev Görünümü gizlendi)")
        Return True
    Else
        WriteLogCustom("❌ Görev çubuğu optimizasyon hatası!")
        Return False
    EndIf
EndFunc

Func ShowFileExtensions()
    WriteLogCustom("📁 Dosya uzantıları gösteriliyor...")

    Local $result = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced", "HideFileExt", "REG_DWORD", 0)

    If $result = 1 Then
        WriteLogCustom("✅ Dosya uzantıları gösterilecek")
        Return True
    Else
        WriteLogCustom("❌ Dosya uzantıları ayarlama hatası!")
        Return False
    EndIf
EndFunc

Func OptimizePerformance()
    WriteLogCustom("⚡ Performans optimizasyonu başlatılıyor...")
    Local $success = True

    ; Registry yollarını oluştur
    RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects", "", "REG_SZ", "")
    RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced", "", "REG_SZ", "")
    RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\DWM", "", "REG_SZ", "")
    RegWrite("HKEY_CURRENT_USER\Control Panel\Desktop", "", "REG_SZ", "")
    RegWrite("HKEY_CURRENT_USER\Control Panel\Desktop\WindowMetrics", "", "REG_SZ", "")

    ; En iyi performans için ayarla
    Local $result1 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects", "VisualFXSetting", "REG_DWORD", 2)

    ; Görsel efekt ayarları
    Local $result2 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced", "ListviewAlphaSelect", "REG_DWORD", 0)
    Local $result3 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced", "TaskbarAnimations", "REG_DWORD", 0)
    Local $result4 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced", "ListviewShadow", "REG_DWORD", 0)
    Local $result5 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced", "MenuShowDelay", "REG_DWORD", 0)

    ; Küçük resimleri ve ClearType'ı koru
    Local $result6 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced", "IconsOnly", "REG_DWORD", 0)

    ; DWM ayarları
    Local $result7 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\DWM", "EnableAeroPeek", "REG_DWORD", 0)
    Local $result8 = RegWrite("HKEY_CURRENT_USER\Software\Microsoft\Windows\DWM", "AlwaysHibernateThumbnails", "REG_DWORD", 0)

    ; Desktop ayarları
    Local $result9 = RegWrite("HKEY_CURRENT_USER\Control Panel\Desktop", "DragFullWindows", "REG_SZ", "0")
    Local $result10 = RegWrite("HKEY_CURRENT_USER\Control Panel\Desktop", "MenuShowDelay", "REG_SZ", "0")
    Local $result11 = RegWrite("HKEY_CURRENT_USER\Control Panel\Desktop", "FontSmoothing", "REG_SZ", "2")
    Local $result12 = RegWrite("HKEY_CURRENT_USER\Control Panel\Desktop", "FontSmoothingType", "REG_SZ", "2")

    ; Pencere animasyonları
    Local $result13 = RegWrite("HKEY_CURRENT_USER\Control Panel\Desktop\WindowMetrics", "MinAnimate", "REG_SZ", "0")

    ; Sistem öncelik ayarları (yönetici yetkisi gerekebilir)
    Local $result14 = RegWrite("HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\PriorityControl", "Win32PrioritySeparation", "REG_DWORD", 2)

    If $result1 = 1 And $result2 = 1 And $result3 = 1 And $result4 = 1 And $result5 = 1 And $result6 = 1 And $result7 = 1 And $result8 = 1 And $result9 = 1 And $result10 = 1 And $result11 = 1 And $result12 = 1 And $result13 = 1 Then
        WriteLogCustom("✅ Performans optimizasyonu tamamlandı")
        WriteLogCustom("   • Görsel efektler optimize edildi")
        WriteLogCustom("   • Animasyonlar kapatıldı")
        WriteLogCustom("   • ClearType ve küçük resimler korundu")
        If $result14 = 1 Then
            WriteLogCustom("   • Sistem öncelik ayarları güncellendi")
        Else
            WriteLogCustom("   ⚠️ Sistem öncelik ayarları güncellenemedi (yönetici yetkisi gerekebilir)")
        EndIf
        Return True
    Else
        WriteLogCustom("❌ Performans optimizasyon hatası!")
        Return False
    EndIf
EndFunc

Func RestartExplorer()
    WriteLogCustom("🔄 Explorer yeniden başlatılıyor...")

    ; Explorer'ı kapat
    Local $pid = ProcessExists("explorer.exe")
    If $pid Then
        ProcessClose($pid)
        WriteLogCustom("   • Explorer kapatıldı")
        Sleep(2000)
    EndIf

    ; Explorer'ı başlat
    Run("explorer.exe")
    WriteLogCustom("   • Explorer yeniden başlatıldı")
    Sleep(1000)

    ; Masaüstünü yenile (SHChangeNotify API çağrısı)
    DllCall("shell32.dll", "none", "SHChangeNotify", "int", 0x8000000, "int", 0, "ptr", 0, "ptr", 0)
    WriteLogCustom("   • Masaüstü yenilendi")

    WriteLogCustom("✅ Explorer başarıyla yeniden başlatıldı")
EndFunc

Func SetWallpaper()
    WriteLogCustom("🖼️ Wallpaper ayarlanıyor...")

    ; Script dizinindeki Wallpaper.jpg dosyasının tam yolunu al
    Local $wallpaperPath = @ScriptDir & "\Wallpaper.jpg"

    ; Dosyanın var olup olmadığını kontrol et
    If Not FileExists($wallpaperPath) Then
        WriteLogCustom("❌ Wallpaper.jpg dosyası bulunamadı: " & $wallpaperPath)
        Return False
    EndIf

    WriteLogCustom("   • Wallpaper dosyası bulundu: " & $wallpaperPath)

    ; Registry ile wallpaper ayarla
    Local $result1 = RegWrite("HKEY_CURRENT_USER\Control Panel\Desktop", "Wallpaper", "REG_SZ", $wallpaperPath)

    ; Wallpaper stilini ayarla (0=Merkez, 1=Döşe, 2=Uzat, 6=Sığdır, 10=Doldur, 22=Yayıl)
    Local $result2 = RegWrite("HKEY_CURRENT_USER\Control Panel\Desktop", "WallpaperStyle", "REG_SZ", "10")
    Local $result3 = RegWrite("HKEY_CURRENT_USER\Control Panel\Desktop", "TileWallpaper", "REG_SZ", "0")

    If $result1 = 1 And $result2 = 1 And $result3 = 1 Then
        WriteLogCustom("   • Registry ayarları güncellendi")

        ; SystemParametersInfo API ile wallpaper'ı anında uygula
        Local $SPI_SETDESKWALLPAPER = 0x0014
        Local $SPIF_UPDATEINIFILE = 0x01
        Local $SPIF_SENDCHANGE = 0x02

        Local $result = DllCall("user32.dll", "bool", "SystemParametersInfoW", _
                               "uint", $SPI_SETDESKWALLPAPER, _
                               "uint", 0, _
                               "wstr", $wallpaperPath, _
                               "uint", BitOR($SPIF_UPDATEINIFILE, $SPIF_SENDCHANGE))

        If $result[0] Then
            WriteLogCustom("✅ Wallpaper başarıyla ayarlandı")
            WriteLogCustom("   • Dosya: " & $wallpaperPath)
            WriteLogCustom("   • Stil: Doldur (ekranı kapla)")
            Return True
        Else
            WriteLogCustom("❌ Wallpaper API çağrısı başarısız!")
            Return False
        EndIf
    Else
        WriteLogCustom("❌ Wallpaper registry ayarları başarısız!")
        Return False
    EndIf
EndFunc