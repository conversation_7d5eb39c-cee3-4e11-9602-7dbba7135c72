@echo off
echo Yonetici Yetkisi Test Scripti
echo =============================
echo.

echo [Test 1] fsutil komutu ile test...
fsutil dirty query %systemdrive% >nul 2>&1
if %errorLevel% equ 0 (
    echo [✓] fsutil testi BASARILI - Yonetici yetkisi var
) else (
    echo [✗] fsutil testi BASARISIZ - Yonetici yetkisi yok
)

echo.
echo [Test 2] openfiles komutu ile test...
openfiles >nul 2>&1
if %errorLevel% equ 0 (
    echo [✓] openfiles testi BASARILI - Yonetici yetkisi var
) else (
    echo [✗] openfiles testi BASARISIZ - Yonetici yetkisi yok
)

echo.
echo [Test 3] net session komutu ile test...
net session >nul 2>&1
if %errorLevel% equ 0 (
    echo [✓] net session testi BASARILI - Yonetici yetkisi var
) else (
    echo [✗] net session testi BASARISIZ - Yonetici yetkisi yok
)

echo.
echo [Test 4] whoami komutu ile test...
whoami /groups | find "S-1-5-32-544" >nul 2>&1
if %errorLevel% equ 0 (
    echo [✓] whoami testi BASARILI - Administrators grubunda
) else (
    echo [✗] whoami testi BASARISIZ - Administrators grubunda degil
)

echo.
echo Test tamamlandi. Hangi yontemler calisiyor gorebilirsiniz.
pause
