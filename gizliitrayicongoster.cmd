@echo off
echo Gizli Simge Gosterici - Otomatik Kurulum Scripti
echo ================================================
echo.

REM Yonetici yetkisi kontrolu - Daha guvenilir yontem
fsutil dirty query %systemdrive% >nul 2>&1
if %errorLevel% neq 0 (
    echo HATA: Bu script yonetici yetkisiyle calistirilmalidir.
    echo Lutfen "Yonetici olarak calistir" secenegini kullanin.
    echo.
    echo Alternatif kontrol yapiliyor...

    REM Alternatif kontrol - openfiles komutu
    openfiles >nul 2>&1
    if %errorLevel% neq 0 (
        echo HATA: Yonetici yetkisi dogrulanamadi.
        pause
        exit /b 1
    ) else (
        echo Alternatif kontrol basarili - devam ediliyor...
    )
)

echo [1/4] PowerShell dosyasi olusturuluyor...

REM PowerShell dosyasini olustur
(
echo # Windows 11 - Tum System Tray Iconlarini Gorunur Yapma Scripti
echo # Yonetici izni gerektirmez - Sessiz calisir
echo.
echo # Hata durumunda scripti durdurma
echo $ErrorActionPreference = "SilentlyContinue"
echo.
echo # Registry yolunu tanimla
echo $registryPath = "HKCU:\Control Panel\NotifyIconSettings"
echo.
echo try {
echo     # NotifyIconSettings altindaki tum subkey'leri al
echo     $subKeys = Get-ChildItem -Path $registryPath -ErrorAction SilentlyContinue
echo.    
echo     if ($subKeys^) {
echo         foreach ($subKey in $subKeys^) {
echo             try {
echo                 # Her subkey icin IsPromoted degerini 1 yap (gorunur^)
echo                 Set-ItemProperty -Path $subKey.PSPath -Name "IsPromoted" -Value 1 -Type DWord -ErrorAction SilentlyContinue
echo             }
echo             catch {
echo                 # Hata durumunda sessizce devam et
echo                 continue
echo             }
echo         }
echo.        
echo         # Explorer.exe'yi yeniden baslatmadan degisiklikleri uygula
echo         # Taskbar'i yenile
echo         $code = @'
echo         [System.Runtime.InteropServices.DllImport("user32.dll"^)]
echo         public static extern bool SetForegroundWindow(IntPtr hWnd^);
echo         [System.Runtime.InteropServices.DllImport("user32.dll"^)]
echo         public static extern IntPtr FindWindow(string lpClassName, string lpWindowName^);
echo '@
echo.        
echo         Add-Type -TypeDefinition $code -ErrorAction SilentlyContinue
echo.        
echo         # Taskbar'i bul ve yenile
echo         $taskbarHandle = [Win32]::FindWindow("Shell_TrayWnd", $null^)
echo         if ($taskbarHandle -ne [IntPtr]::Zero^) {
echo             [Win32]::SetForegroundWindow($taskbarHandle^)
echo         }
echo.        
echo         # Alternatif yenileme yontemi
echo         Get-Process explorer -ErrorAction SilentlyContinue ^| ForEach-Object { 
echo             $_.Refresh(^) 
echo         }
echo.        
echo     }
echo }
echo catch {
echo     # Genel hata durumunda sessizce cik
echo     exit 0
echo }
echo.
echo # Script basariyla tamamlandi
echo exit 0
) > "C:\gizlisimgegoster.ps1"

if exist "C:\gizlisimgegoster.ps1" (
    echo [✓] PowerShell dosyasi basariyla olusturuldu: C:\gizlisimgegoster.ps1
) else (
    echo [✗] PowerShell dosyasi olusturulamadi!
    pause
    exit /b 1
)

echo.
echo [2/4] Eski gorevler siliniyor...

REM Eski gorevleri sil
schtasks /delete /tn "GizliSimgeGoster_Baslangic" /f >nul 2>&1
schtasks /delete /tn "GizliSimgeGoster_Saatlik" /f >nul 2>&1

echo [✓] Eski gorevler temizlendi

echo.
echo [3/4] Baslangic gorevi olusturuluyor...

REM Baslangic gorevini olustur
schtasks /create /tn "GizliSimgeGoster_Baslangic" /tr "powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -File C:\gizlisimgegoster.ps1" /sc onlogon /rl highest /f >nul 2>&1

if %errorLevel% equ 0 (
    echo [✓] Baslangic gorevi basariyla olusturuldu
) else (
    echo [✗] Baslangic gorevi olusturulamadi!
)

echo.
echo [4/4] Saatlik gorev olusturuluyor...

REM Saatlik gorevi olustur
schtasks /create /tn "GizliSimgeGoster_Saatlik" /tr "powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -File C:\gizlisimgegoster.ps1" /sc hourly /mo 1 /rl highest /f >nul 2>&1

if %errorLevel% equ 0 (
    echo [✓] Saatlik gorev basariyla olusturuldu
) else (
    echo [✗] Saatlik gorev olusturulamadi!
)

echo.
echo ================================================
echo KURULUM TAMAMLANDI!
echo ================================================
echo.
echo Olusturulan dosya: C:\gizlisimgegoster.ps1
echo.
echo Olusturulan gorevler:
echo - GizliSimgeGoster_Baslangic (sistem acilisinda calisir)
echo - GizliSimgeGoster_Saatlik (her saatte calisir)
echo.
echo Gorevleri test etmek icin:
echo schtasks /run /tn "GizliSimgeGoster_Baslangic"
echo schtasks /run /tn "GizliSimgeGoster_Saatlik"
echo.
echo Gorevleri silmek icin:
echo schtasks /delete /tn "GizliSimgeGoster_Baslangic" /f
echo schtasks /delete /tn "GizliSimgeGoster_Saatlik" /f
echo.

pause